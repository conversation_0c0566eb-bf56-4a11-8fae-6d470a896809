// components/teacher/TeacherStudentsPageWithData.client.tsx
'use client';

import { useMemo } from 'react';
import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';
import { useThrottled } from '../../hooks/useDebounced';
import { usePerformanceMonitor } from '../debug/PerformanceMonitor.client';
import StudentManagementTabsWithData from '../auth/teacher/students/student-management-tabs-with-data.client';

interface User {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
  teacherId?: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  createdAt?: string;
}

interface TeacherStudentsPageWithDataProps {
  allUsers: User[];
}

const TeacherStudentsPageWithData = ({ allUsers }: TeacherStudentsPageWithDataProps) => {
  const { slug } = useParams();
  const { user } = useAuth();

  // Throttle the allUsers prop to reduce the frequency of expensive computations
  // This prevents the component from recomputing student arrays on every Blade revalidation
  const throttledAllUsers = useThrottled(allUsers, 2000); // Only update every 2 seconds max

  // Monitor performance to track improvements
  usePerformanceMonitor('TeacherStudentsPageWithData', [allUsers.length, throttledAllUsers.length, user?.id]);

  // Memoize the access check to prevent unnecessary re-renders
  const accessCheck = useMemo(() => {
    if (!user) {
      return { hasAccess: false, reason: 'loading' };
    }

    if (user.slug !== slug) {
      return { hasAccess: false, reason: 'unauthorized' };
    }

    return { hasAccess: true, reason: null };
  }, [user?.id, user?.slug, slug]); // Only depend on stable user properties

  // Handle access control
  if (!accessCheck.hasAccess) {
    if (accessCheck.reason === 'loading') {
      return (
        <div className="p-6">
          <h1 className="text-2xl font-bold">Loading...</h1>
        </div>
      );
    }

    if (accessCheck.reason === 'unauthorized') {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600">You can only access your own profile.</p>
          </div>
        </div>
      );
    }
  }

  // Find the teacher by slug - memoized to prevent unnecessary re-computation
  const teacher = useMemo(() =>
    throttledAllUsers.find(u => u.slug === slug && u.role === 'teacher'),
    [throttledAllUsers, slug]
  );

  if (!teacher) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Teacher Not Found</h1>
          <p className="text-gray-600">The requested teacher profile could not be found.</p>
        </div>
      </div>
    );
  }

  // Memoize expensive array operations to prevent unnecessary re-renders
  // This is the key fix - these operations were running on every render
  const { activeStudents, inactiveStudents } = useMemo(() => {
    console.log('[PERFORMANCE] Computing student arrays for teacher:', teacher.id);

    // Get ALL students for this teacher (both active and inactive)
    const allStudentsForTeacher = throttledAllUsers.filter(u =>
      u.teacherId === teacher.id &&
      u.role === 'student'
    ).sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    // Separate active and inactive students
    const active = allStudentsForTeacher.filter(u => u.isActive !== false);
    const inactive = allStudentsForTeacher.filter(u => u.isActive === false);

    console.log('[PERFORMANCE] Computed', active.length, 'active and', inactive.length, 'inactive students');

    return {
      activeStudents: active,
      inactiveStudents: inactive
    };
  }, [throttledAllUsers, teacher.id]); // Only recompute when throttledAllUsers or teacher.id changes

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-4">Student Management</h1>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Manage your students and their class assignments.
        </p>
      </div>

      {/* Use the student management tabs component with data */}
      <StudentManagementTabsWithData
        students={activeStudents}
        removedStudents={inactiveStudents}
        teacher={teacher}
      />
    </div>
  );
};

export default TeacherStudentsPageWithData;
