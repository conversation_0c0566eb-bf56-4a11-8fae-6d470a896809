// components/debug/PerformanceMonitor.client.tsx
'use client';

import { useEffect, useRef } from 'react';

interface PerformanceMonitorProps {
  name: string;
  data?: any;
  enabled?: boolean;
}

/**
 * A simple performance monitoring component to track when components re-render
 * and how frequently data changes occur during Blade revalidations
 */
export default function PerformanceMonitor({ 
  name, 
  data, 
  enabled = process.env["NODE_ENV"] === 'development' 
}: PerformanceMonitorProps) {
  const renderCount = useRef(0);
  const lastDataHash = useRef<string>('');
  const lastRenderTime = useRef<number>(Date.now());

  useEffect(() => {
    if (!enabled) return;

    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    lastRenderTime.current = now;

    // Create a simple hash of the data to detect changes
    const currentDataHash = data ? JSON.stringify(data).slice(0, 100) : 'no-data';
    const dataChanged = currentDataHash !== lastDataHash.current;
    lastDataHash.current = currentDataHash;

    console.log(`[PERF] ${name} - Render #${renderCount.current} (${timeSinceLastRender}ms since last)`, {
      dataChanged,
      dataSize: data ? (Array.isArray(data) ? data.length : Object.keys(data).length) : 0,
      timestamp: new Date().toISOString().split('T')[1].split('.')[0]
    });
  });

  // This component doesn't render anything
  return null;
}

/**
 * Hook version of the performance monitor
 */
export function usePerformanceMonitor(
  name: string, 
  dependencies: any[] = [], 
  enabled = process.env["NODE_ENV"]  === 'development'
) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef<number>(Date.now());

  useEffect(() => {
    if (!enabled) return;

    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    lastRenderTime.current = now;

    console.log(`[PERF] ${name} - Effect triggered #${renderCount.current} (${timeSinceLastRender}ms since last)`, {
      dependencies: dependencies.map(dep => typeof dep === 'object' ? 
        (Array.isArray(dep) ? `Array(${dep.length})` : `Object(${Object.keys(dep).length})`) : 
        String(dep)
      ),
      timestamp: new Date().toISOString().split('T')[1].split('.')[0]
    });
  }, dependencies);
}
